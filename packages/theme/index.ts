import { createTheme } from '@mui/material/styles';
import { baseFontFamily, fontSize, fontWeight } from './style.typography.ts';
import {
  colors,
  brand,
  greyScaleColour,
} from './style.palette.ts';

const theme = createTheme({
  breakpoints: {
    values: {
      xs: 0, // Extra small devices (up to 767px)
      sm: 768, // Small devices (768px and up)
      md: 1024, // Medium devices (1024px and up)
      lg: 1600, // Large devices (1600px and up)
      xl: 1921, // Extra large devices (1920px and up)
    },
  },
  typography: {
    fontFamily: baseFontFamily,
    h1: {
      fontSize: fontSize.h1,
      fontWeight: fontWeight.semiBold,
    },
    h2: {
      fontSize: fontSize.h2,
      fontWeight: fontWeight.medium,
    },
    h3: {
      fontSize: fontSize.h3,
      fontWeight: fontWeight.medium,
    },
    h4: {
      fontSize: fontSize.h4,
      fontWeight: fontWeight.medium,
    },
    h5: {
      fontSize: fontSize.h5,
      fontWeight: fontWeight.semiBold,
    },
    body1: {
      fontSize: fontSize.b1,
      fontWeight: fontWeight.regular,
    },
    body2: {
      fontSize: fontSize.b2,
      fontWeight: fontWeight.regular,
    },
    subtitle1: {
      fontSize: fontSize.b1,
      fontWeight: fontWeight.medium,
    },
    subtitle2: {
      fontSize: fontSize.b2,
      fontWeight: fontWeight.medium,
    },
  },
  palette: {
    primary: {
      main: brand.primaryMain,
      contrastText: brand.white
    },
    secondary: {
      main: brand.white,
    },
  },
  shape: {
    borderRadius: 6,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          margin: '0px',
          fontFamily: baseFontFamily,
          fontSize: fontSize.h5,
          fontWeight: fontWeight.medium,
          color: greyScaleColour.white100,
          borderRadius: '4px',
          boxShadow: 'none',
          padding: '8px 16px',
          width: '100%',
          lineHeight: '24px',
        },
        containedPrimary: {
          backgroundColor: brand.primary100,
          lineHeight:'26px',
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: brand.primaryHover,
          },
          '&.Mui-disabled': {
            color: greyScaleColour.grey60,
            backgroundColor: greyScaleColour.grey20,
          },
        },
        containedError: {
          backgroundColor: colors.errorDefault,
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: colors.errorHover,
          },
          '&.Mui-disabled': {
            color: greyScaleColour.grey80,
            backgroundColor: greyScaleColour.grey20,
          },
        },
        outlinedSecondary: {
          backgroundColor: brand.white,
          color: brand.black,
          border: `1px solid ${brand.black}`,
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: brand.black,
            color: brand.white,
          },
          '&.Mui-disabled': {
            color: greyScaleColour.grey80,
            backgroundColor: brand.white,
          },
        },
        sizeMedium: {
          fontWeight: fontWeight.medium,
          borderRadius: '4px',
        },
        textPrimary: {
          backgroundColor: 'inherit',
          color: brand.white,
          fontSize: fontSize.b2,
          fontWeight: fontWeight.regular,
          padding: '0 !important',
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: 'inherit',
          },
        },
        textSecondary: {
          backgroundColor: 'inherit',
          color: brand.black,
          fontSize: fontSize.b1,
          fontWeight: fontWeight.regular,
          padding: '0 !important',
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: 'inherit',
          },
        },
        outlinedPrimary: {
          borderRadius: '4px',
          borderColor: brand.black,
          color: brand.black,
          background: 'inherit',
          '&:hover': {
            background: 'inherit',
            boxShadow: 'none',
          },
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          color: brand.black,
          background:greyScaleColour.grey10,

          '&.Mui-checked': {
            color: brand.primaryMain,
            border: `2px solid ${brand.primary100}`
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '10px',
          borderBottom: 'none',
          
        },
        head: {
          padding: '12px 24px',
          color: greyScaleColour.grey100,
          backgroundColor: colors.theadBg,
          fontWeight:fontWeight.medium,
          fontSize:fontSize.h5,
          lineHeight:'21px'
        },
        body: {
          lineHeight: '21px',
          padding:'10px 24px',
          borderBottom: '1px solid #EEEEEE',
          fontSize: fontSize.b1,
          fontWeight: fontWeight.regular,
          color:brand.black
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '999px',
          padding: '4px 12px',
          '&.MuiChip-label': {
            padding: 0,
          },
        },
        label: {
          padding: 0,
          fontSize: fontSize.b2,
          fontWeight: fontWeight.medium,
        },
        filledPrimary: {
          backgroundColor: brand.primary40,
          color: brand.primary100,
        },
        outlinedPrimary: {
          border: `1px solid ${brand.primary100}`,
          color: brand.primary100,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '10px',
          boxShadow: 'none',
          border: `1px solid ${greyScaleColour.grey80}`,
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: '0',
          display: 'flex',
          flexDirection: 'column',
          '&:last-child': {
            paddingBottom: '0px',
          },
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: fontSize.b1,
          borderRadius: '10px',
          borderColor: '#D0D0D2',
          backgroundColor: brand.white,
          border: 'none',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: `${brand.primary100} !important`,
          },
          '&.Mui-error': {
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: `${colors.errorDefault} !important`,
            },
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderWidth: '1px !important',
            borderColor: `${brand.primary100} !important`,
          },
          '&.Mui-error.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: `${colors.errorDefault} !important`,
          },
          '&.Mui-disabled': {
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#BBBBBF !important',
            },
          },
        },
        input: {
          padding: '14px 16px',
        },
        inputMultiline: {
          padding: '0px !important',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        input: {
          padding: '14px 16px',
          fontSize: fontSize.h5,
        },
        notchedOutline: {
          borderColor: greyScaleColour.grey40,
          borderRadius: '4px',
          borderWidth: '1px !important',
        },
      },
    },
    MuiFormControl: {},
    MuiInputLabel: {
      styleOverrides: {
        root: {
          top: -2,
          color: colors.label,
          fontSize: fontSize.b1,
          fontWeight: fontWeight.regular,
          '&.Mui-focused': {
            color: brand.primary100,
          },
          '&.Mui-error': {
            color: colors.errorDefault,
          },
        },
        shrink: {
          top: 0,
        },
        asterisk: {
          color: colors.errorDefault,
        },
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          textDecoration: 'none',
          color: greyScaleColour.grey100,
          '&:hover': {
            color:brand.primaryMain,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          width: '100%',
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            padding: '6px',
          },
        },
        option: {
          '&.Mui-focused': {
            backgroundColor: `${greyScaleColour.grey10} !important`,
          },
          '&[aria-selected="true"]': {
            backgroundColor: `${brand.primaryMain} !important`,
            color: `${brand.white} !important`,
          },
        },
        paper: {
          boxShadow: '0px 2px 15px 0px rgba(0, 0, 0, 0.12)',
        },
        input: {
          minWidth: '0px !important',
        },
        tag: {
          display: 'flex',
          padding: '2px 10px',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: brand.primaryMain,
          color: brand.white,
          borderRadius: '60px',
        },
      },
    },
  },
});

export default theme;
