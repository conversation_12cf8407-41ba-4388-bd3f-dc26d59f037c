export interface AdminColor {
  background: string;
}
export const adminColor: AdminColor = {
  background: '#000000',
};

export interface EmployeeColor {
  background: string;
}
export const employeeColor: EmployeeColor = {
  background:'#F7F7F7'
}

export interface Colors {
  black: string;
  success: string;
  tableBorder: string;
  yellow100: string;
  yellow80: string;
  darkNavy100: string;
  darkNavy80: string;
  ebony: string;
  ebonylight: string;
  ashgrey: string;
  cream: string;
  alabaster: string;
  lightAshGray: string;
  warning: string;
  sidebar: string;
  errorHover: string;
  errorDefault: string;
  errorBg: string;
  successHover: string;
  successDefault: string;
  successBg: string;
  informationDefault: string;
  informationBg: string;
  border: string;
  bgGrey: string;
  bg: string;
  warningBg: string;
  warningDefault: string;
  label: string;
  theadBg: string;
  info: string;
  shadowLine: string;
  greyBorder: string;
  greyBorder2: string;
}
export const colors: Colors = {
  black: '#000000',
  success: '#23C272',
  tableBorder: '#8A8A8A',
  yellow100: '#FFD61D',
  yellow80: '#FED84F',
  darkNavy100: '#01106B',
  darkNavy80:'#041066',
  ebony:'#2D3136',
  ebonylight:'#2E3135',
  ashgrey:'#B8ADA3',
  cream:'#F1E4DB',
  alabaster:'#EFE5DC',
  lightAshGray:'#B6ADA4',
  warning:'#EF6C00',
  sidebar: '#F8F8F8',
  errorHover: '#D13E1D',
  errorDefault: '#FF5630',
  errorBg: '#FFEEEA',
  successHover:'#118D57',
  successDefault: '#23C272',
  successBg: '#E9F9F1',
  informationDefault: '#0077B8',
  informationBg: '#CCE4F1',
  border:'#8A8A8A',
  bgGrey:'#F7F7F7',
  bg:'#FFFFFF',
  warningBg:'#FFF4E5',
  warningDefault:'#EF6C00',
  label:'#737373',
  theadBg:'#5151510F',
  info:'#5467C7',
  shadowLine:'#EEEEEE',
  greyBorder:'#D9D9D9',
  greyBorder2:'#CCCCCC'
};

export interface Brand {
  primaryMain: string;
  primary100: string;
  primary40: string;
  primary20: string;
  primary05: string;
  secondaryMain: string;
  primaryHover: string;
  black: string;
  white: string;
}
export const brand: Brand = {
  primaryMain: '#54AFC7',
  primary100: '#54AFC7',
  primary40: '#54AFC766',
  primary20: '#EDFCFF',
  primary05: '#54AFC70D',
  secondaryMain: '#FFFFFF',
  primaryHover:'#448EA1',
  black: '#1A1A1A',
  white: '#FFFFFF',
};

export interface GreyScaleColour {
  grey100: string;
  grey80: string;
  grey60: string;
  grey40: string;
  grey25: string;
  grey20: string;
  grey10: string;
  white100: string;
}
export const greyScaleColour: GreyScaleColour = {
  grey100: '#515151',
  grey80: '#515151CC',
  grey60: '#51515199',
  grey40: '#51515166',
  grey25: '#51515140',
  grey20:'#51515133',
  grey10:'#5151511A',
  white100: '#FFFFFF',
}

export interface OtherColour {
  completedBg: string;
  completedDefault: string;
  inProgressBg: string;
  inProgressDefault: string;
  notStartedBg: string;
  notStartedDefault: string;
  green: string;
  purple: string;
};
export const otherColour: OtherColour = {
  completedBg: '#E9F9F1',
  completedDefault: '#118D57',
  inProgressBg: '#FFF4E5',
  inProgressDefault: '#EF6C00',
  notStartedBg: '#5151511A',
  notStartedDefault: '#515151',
  green:'#44A14F',
  purple:'#444CA1'
};
