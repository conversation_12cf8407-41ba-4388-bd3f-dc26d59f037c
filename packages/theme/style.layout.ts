export interface Breakpoints {
  phone: number;
  tablet: number;
  desktop: number;
}

export const breakpoints: Breakpoints = {
  phone: 767,
  tablet: 1023,
  desktop: 1199,
};

export interface RespondTo {
  smOnly: string;
  smUp: string;
  mdUp: string;
  mdDown: string;
  lgUp: string;
  lgDown: string;
  screenDown: (sizeInPixel: number) => string;
  screenUp: (sizeInPixel: number) => string;
  screenRange: (sizeInPixelL: number, sizeInPixelH: number) => string;
  screenHight: (sizeInPixel: number) => string;
}

export const respondTo: RespondTo = {
  smOnly: `@media only screen and (max-width: ${breakpoints.phone}px)`,
  smUp: `@media only screen and (min-width: ${breakpoints.phone + 1}px)`,
  mdUp: `@media only screen and (min-width: ${breakpoints.tablet + 1}px)`,
  mdDown: `@media only screen and (max-width: ${breakpoints.tablet}px)`,
  lgUp: `@media only screen and (min-width: ${breakpoints.desktop + 1}px)`,
  lgDown: `@media only screen and (max-width: ${breakpoints.desktop}px)`,
  screenDown: (sizeInPixel: number) =>
    `@media only screen and (max-width: ${sizeInPixel}px)`,
  screenUp: (sizeInPixel: number) =>
    `@media only screen and (min-width: ${sizeInPixel}px)`,
  screenRange: (sizeInPixelL: number, sizeInPixelH: number) =>
    `@media only screen and (min-width: ${sizeInPixelL}px) and (max-width: ${sizeInPixelH}px)`,
  screenHight: (sizeInPixel: number) =>
    `@media only screen and (max-height: ${sizeInPixel}px)`,
};
