export const baseFontFamily: string = 'poppins';
export const baseFontSize: string = '18px';

export interface FontWeight {
  light: number;
  regular: number;
  medium: number;
  semiBold: number;
  bold: number;
  extraBold: number;
}

export const fontWeight: FontWeight = {
  light: 300,
  regular: 400,
  medium: 500,
  semiBold: 600,
  bold: 700,
  extraBold: 800,
};

export interface FontSize {
  b0: string;
  b1: string;
  b2: string;
  b3: string;
  h5: string;
  h4: string;
  h3: string;
  h2: string;
  h1: string;
  subTitle: string;
  title: string;
}

export const fontSize: FontSize = {
  b0: '18px',
  b1: '14px',
  b2: '12px',
  b3: '10px',
  h5: '16px',
  h4: '20px',
  h3: '24px',
  h2: '28px',
  h1: '32px',
  subTitle:'40px',
  title:'47px',
};
