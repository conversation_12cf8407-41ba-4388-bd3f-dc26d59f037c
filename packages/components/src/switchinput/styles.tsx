import {styled} from 'styled-components';
import { Typography } from '@mui/material';
import { brand, colors, greyScaleColour } from '@mono/theme/style.palette.ts';
import { FormControlLabel } from '@mui/material';
import {
  fontSize,
  fontWeight,
  baseFontFamily,
} from '@mono/theme/style.typography.ts';

export const StyledContainer = styled.div`
  display: flex;
  width: 100%;
`;

export const StyledError = styled(Typography)`
  margin-top: 8px !important;
  color: ${colors.errorDefault};
`;

export const StyledFormControlLabel = styled(FormControlLabel)`
  margin-left: 0px !important;
  .MuiFormControlLabel-label {
    font-size: ${fontSize.b1};
    font-weight: ${fontWeight.medium};
    font-family: ${baseFontFamily};
    color: ${brand.black};
  }
`;
