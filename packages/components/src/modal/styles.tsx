import { Typography } from '@mui/material';
import { css, styled } from 'styled-components';
import {
  brand,
  greyScaleColour,
  colors,
} from '@mono/theme/style.palette.ts';
import { fontWeight } from '@mono/theme/style.typography.ts';
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined';

export const StyledContainer = styled.div<{ fitContent?: boolean; reduceMargin?: boolean }>`
  width: ${({ fitContent }) => (fitContent ? 'fit-content' : 'min(40%, 600px)')}; 
  margin: 0 auto;
  margin-top: ${({ reduceMargin }) => (reduceMargin ? '1vh' : 'min(10vh, 128px)')}; 
  display: flex;
  flex-direction: column;
  max-height: 120vh;
  @media (max-height: 656px) {
    margin-top: 2vh;
  }
`;

export const StyledHeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  font-weight: 600;
  font-size:20px;
  font-family: 'poppins';
  line-height: 26px;
  background-color: #F6F6F6;
  color: ${brand.black};
  border-bottom:1px solid ${greyScaleColour.grey10};
  margin:auto;
`;

export const StyledHeading = styled(Typography)`
  color: ${brand.black};
  line-height: 26px !important;
  font-size:20px !important;
  font-weight:600;
`;

export const StyledSubHeading = styled(Typography)`
  color: ${greyScaleColour.grey100};
  font-weight: ${fontWeight.medium} !important;
`;

export const StyledCloseContainer = styled.div`
  cursor: pointer;
  margin-left: 16px;
  margin-top: 5px;
`;

export const StyledHeadingImgContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const StyledHeadingImg = styled.img`
  width: 72px;
`;

export const StyledButtonContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const StyledCloseButton = styled(CloseOutlinedIcon)``;
