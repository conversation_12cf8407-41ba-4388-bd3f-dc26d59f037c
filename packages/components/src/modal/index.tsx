import React from 'react';
import type { JSX }  from 'react';
import { Modal as MuiModal } from '@mui/material';
import Card from '../card/index.tsx';
import {
  StyledContainer,
  StyledHeaderContainer,
  StyledSubHeading,
  StyledHeadingImgContainer,
  StyledHeadingImg,
  StyledCloseContainer,
  StyledButtonContainer,
  StyledHeading,
  StyledCloseButton,
} from './styles.tsx';

interface ModalCustomProps {
  children?: JSX.Element | JSX.Element[];
  show?: boolean;
  onClose?: () => void;
  heading?: string;
  headingImgSrc?: string;
  subHeading?: string;
  fitContent?: boolean;
  hasCloseIcon?: boolean;
  isCreateOrEditForm?: boolean;
}

const Modal = ({
  children,
  heading,
  show,
  onClose,
  subHeading,
  fitContent,
  headingImgSrc,
  isCreateOrEditForm = false,
  hasCloseIcon = true,
}: Readonly<ModalCustomProps>) => {
  return (
  <MuiModal
    open={!!show}
    onClose={(event: React.MouseEvent<HTMLElement>, reason: string) => {
      if (
        isCreateOrEditForm &&
        (reason === 'escapeKeyDown' || reason === 'backdropClick')
      ) {
        event.preventDefault();
      } else {
        onClose();
      }
    }}
  >
    <StyledContainer 
      fitContent={fitContent} 
      //reduceMargin={makeModalFitToScreen}
    >
      <Card
        contentCss={{
          overflowY: 'auto',
          maxHeight: 'calc(100vh - 174px)',
          padding: 0,
          paddingBottom:0
        }}
        noHeaderPadding
        header={
          <StyledHeaderContainer>
            {heading && <StyledHeading variant="h3">{heading}</StyledHeading>}
            {subHeading && (
              <StyledSubHeading variant="body1">{subHeading}</StyledSubHeading>
            )}
            {headingImgSrc && (
              <StyledHeadingImgContainer>
                <StyledHeadingImg src={headingImgSrc} />
              </StyledHeadingImgContainer>
            )}
            <StyledButtonContainer>
              {hasCloseIcon && (
                <StyledCloseContainer onClick={onClose}>
                  <StyledCloseButton />
                </StyledCloseContainer>
              )}
            </StyledButtonContainer>
          </StyledHeaderContainer>
        }
      >
        {children}
      </Card>
    </StyledContainer>
  </MuiModal>
)};

export default Modal;
