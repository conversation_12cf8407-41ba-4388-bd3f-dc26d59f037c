import { TextField, Typography } from '@mui/material';
import {styled, css } from 'styled-components';
import { colors } from '@mono/theme/style.palette.ts';
import { respondTo } from '@mono/theme/style.layout.ts';

export const StyledInputContainer = styled.div<{
  maxWidth?: string;
  isHeader?: boolean;
}>`
  width: ${(prop) => prop.isHeader && '100%'};
  ${({ maxWidth }) => maxWidth
    && css`
      & .MuiFormControl-root {
        width: ${maxWidth};
        & .MuiInputBase-root {
          max-width: ${maxWidth} !important;
          height: 48px !important;
        }

        ${respondTo.mdDown} {
          width: 100%;
          max-width: 340px !important;
        }
      }
    `}
`;

export const StyledError = styled(Typography)`
  text-align: left;
  color: ${colors.errorDefault};
  margin-top: 8px !important;
`;

export const StyledTextField = styled(TextField)`
  input {
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      display: none;
      margin: 0;
    }
  }
`;
