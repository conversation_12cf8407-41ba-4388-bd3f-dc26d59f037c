import React from 'react';
import type { TextFieldProps } from '@mui/material';
import { StyledError, StyledInputContainer, StyledTextField } from './styles.tsx';

interface Props {
  value?: string;
  onChange?: any;
  error?: string;
  disableErrorMode?: boolean;
  maxWidth?: string;
  isHeader?: boolean;
}

const TextInput: React.FC<Props & TextFieldProps> = ({
  value,
  onChange,
  error,
  disableErrorMode,
  maxWidth,
  isHeader,
  ...props
}) => (
  <StyledInputContainer isHeader={isHeader} maxWidth={maxWidth}>
    <StyledTextField
      {...props}
      value={value || ''}
      error={disableErrorMode ? undefined : !!error}
      onChange={(event) => {
        if (onChange) {
          onChange(event?.currentTarget?.value);
        }
      }}
    />
    {!disableErrorMode && error && (
      <StyledError variant="body2">{error}</StyledError>
    )}
  </StyledInputContainer>
);

export default TextInput;
