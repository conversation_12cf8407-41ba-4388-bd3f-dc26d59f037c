import React, { useCallback } from 'react';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import { StyledDatePicker } from './styles.tsx';
import { greyScaleColour } from '@mono/theme/style.palette.ts';
import { StyledError, StyledInputContainer } from '../materialTextInput/styles.tsx';

import moment from 'moment';

interface Props {
  label?: string;
  value?: string;
  onChange?: (newValue: string) => void;
  error?: string;
  disableErrorMode?: boolean;
  required?: boolean;
  readOnly?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  dateFormat?: string;
  onReadOnlyCtaClick?: () => void;
}

const DateInput: React.FC<Props> = ({
  label,
  error,
  value,
  onChange,
  disableErrorMode,
  required,
  readOnly,
  disabled,
  fullWidth,
  dateFormat,
  onReadOnlyCtaClick,
  ...props
}) => {
  // Using useCallback to prevent unnecessary function re-creation on each render
  const handleChange = useCallback(
    (newValue: string) => {
      if (onChange && newValue !== value) {
        onChange(newValue);  // Only update when the value actually changes
      }
    },
    [onChange, value]  // Dependencies: onChange and value to avoid infinite loops
  );

  return (
    <StyledInputContainer>
      <StyledDatePicker
        {...props}
        label={label}
        format={dateFormat || 'DD/MM/YYYY'}
        fullWidth={fullWidth}
        disabled={disabled}
        value={value ? moment(value, dateFormat || 'YYYY-MM-DD') : null} // Binding the value directly to the component
        onChange={handleChange} // Using the memoized callback here
        minDate={moment()}
        slotProps={{
          textField: {
            required,
            error: disableErrorMode ? undefined : !!error,
          },
        }}
        slots={{
          openPickerIcon: () => (
            <CalendarTodayOutlinedIcon
              style={{
                color: greyScaleColour.grey100,
              }}
            />
          ),
        }}
      />
      {!disableErrorMode && (
        <StyledError variant="body2">{error || ''}</StyledError>
      )}
    </StyledInputContainer>
  );
};

export default DateInput;
