import React from "react";
import type { JSX } from "react";
import { StyledError } from "../materialTextInput/styles.tsx";
import { Checkbox, FormControlLabel } from "@mui/material";
import { StyledCheckBoxWrapper, StyledCheckImg, StyledLabel } from "./styles.tsx";

interface Props {
    label?: (string | JSX.Element);
    value?: boolean;
    onChange?: any;
    error?: string;
    disableErrorMode?: boolean;
    disabled?:boolean;
}

const CheckedIcon: React.FC<{ checked?: boolean }> = ({
    checked
}) => {
    return (
        <StyledCheckImg
            src={`/assets/images/${checked ? 'checked.svg' : 'unchecked.svg'}`}
        />
        
    );
};

const MuiCheckBox: React.FC<Props> = ({
    label, value, onChange,
    error, disableErrorMode,disabled
}) => {
    const labelIsString = typeof label === 'string';
    return (
        <StyledCheckBoxWrapper>
            <FormControlLabel
                sx={{
                    marginLeft : '-8px',
                    marginRight:'0px'
                }}
                disabled={disabled}
                label={(labelIsString ? (
                    <StyledLabel variant="body2">{label}</StyledLabel>
                ) : label)}
                control={
                    <Checkbox
                        checkedIcon={<CheckedIcon checked />}
                        icon={<CheckedIcon />}
                        checked={!!value}
                        onChange={() => {
                            if (onChange) {
                                onChange(!value)
                            }
                        }}
                    />
                }
            />
            {!disableErrorMode && error && <StyledError variant='body2'>{error}</StyledError>}
        </StyledCheckBoxWrapper>
    )
}

export default MuiCheckBox;