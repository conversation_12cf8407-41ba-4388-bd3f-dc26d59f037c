import {styled, css } from 'styled-components';
import { FormControlLabel, Typography } from '@mui/material';
import {
  fontSize,
  fontWeight,
  baseFontFamily,
} from '@mono/theme/style.typography.ts';
import {
  brand,
  colors,
  greyScaleColour,
} from '@mono/theme/style.palette.ts';

export const StyledSidebarContainer = styled.div<{ sidebarOpen?: boolean }>`
  display: flex;
  width: ${(props) => (!props.sidebarOpen ? '330px' : '64px')};
  transition: all 0.2s ease-in-out;
  background-color: ${colors.sidebar};
  position: relative;
  padding: 30px 20px 0px 20px;
  min-height: calc(100vh - 80px);
`;

export const StyledSidebarInnerContainer = styled.div`
  height: 100%;
  width: 290px;
  display: flex;
  flex-direction: column;
`;

export const StyledHeadingIconContainer = styled.div`
  display: flex;
  height: 30px;
`;

export const StyledSidebarIcon = styled.img`
    object-fit: contain;
    width: auto;
    height: 139px;
`;

export const StyledMenuItemContainer = styled.div`
  display: flex;
  margin-top: 60px;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
`;

export const StyledMenuItemBoxContainer = styled.div`
  font-weight: ${fontWeight.semiBold};
  font-size: ${fontSize.b2};
  line-height: 20px;
  letter-spacing: 2px;
`;

export const StyledSubMenuItemBoxContainer = styled.div`
  display:flex;
  flex-direction:row;
`;

export const StyledSubMenuItemContainer = styled.a<{ active?: boolean }>`
  padding: 11px 16px;
  display: flex;
  gap: 20px;
  cursor: pointer;
  text-decoration: none;
  background-color: ${(props) => props.active && brand.primary100};
  border-width: ${(props) => props.active && '0px 0px 0px 3px'};
  border-radius: ${(props) => props.active && '0px 6px 6px 0px'};
  border-style: ${(props) => props.active && 'solid'};
  border-color: ${(props) => props.active && brand.primaryMain};
  color: ${(props) =>
    props.active ? brand.primaryMain : brand.black};
`;

export const StyledSubMenuSecondaryContainer = styled.a<{ active?: boolean }>`
  display: flex;
  gap: 20px;
  cursor: pointer;
  text-decoration: none;
  background-color: ${(props) => props.active && brand.primary40};
  border-width: ${(props) => props.active && '0px 0px 0px 3px'};
  border-radius: ${(props) => props.active && '0px 6px 6px 0px'};
  border-style: ${(props) => props.active && 'solid'};
  border-color: ${(props) => props.active && brand.primaryMain};
  color: ${(props) =>
    props.active ? brand.primaryMain : brand.black};
`;

export const StyledIconContainer = styled.div`
  height: 25px;
  width: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const StyledSecondarySubMenuContainer = styled.a<{ active?: boolean }>`
  padding: 11px 16px;
  display: flex;
  gap: 20px;
  cursor: pointer;
  text-decoration: none;
  color: ${(props) =>
    props.active ? brand.primaryMain : brand.black};
  user-select: none;
`;

export const StyledSecondaryMenuSubMenuContainer = styled.div`
  padding: 16px 16px;
  gap: 10px;
  border: 1px solid ${brand.primary100};
  border-radius: 10px;
  margin: 6px 0px;
  display: flex;
  flex-direction: column;
  background-color: ${greyScaleColour.white100};
`;

export const StyledSubMenuItemText = styled(Typography)`
  font-size: ${fontSize.h5} !important;
  font-weight: ${fontWeight.regular} !important;
  line-height: 24px !important;
`;

export const StyledSubMenuSecondaryItemText = styled(Typography)`
  font-size: ${fontSize.b1} !important;
  font-weight: ${fontWeight.regular} !important;
  line-height: 20px !important;
`;

export const StyledMenuItem = styled.a<{ active?: boolean }>`
  padding: 12px 24px;
  display: flex;
  width: auto;
  gap: 10px;
  text-decoration: none;
  align-items: center;
  ${({ active }) =>
    active
      ? css`
          background: ${brand.primaryMain};
          color: ${greyScaleColour.grey100};
          font-weight: ${fontWeight.bold};
          border-radius: 6px;
        `
      : css`
          /* min-width: 191px; */
        `}
`;

export const StyledIcon = styled.img<{
  position?: string;
  right?: string;
  top?: string;
  padding?: string;
  cursor?: string;
  height?: string;
  width?: string;
}>`
  position: ${(props) => props.position};
  right: ${(props) => props.right};
  top: ${(props) => props.top};
  padding: ${(props) => props.padding};
  cursor: ${(props) => props.cursor || 'pointer'};
  height: ${(props) => props.height};
  width: ${(props) => props.width};
  max-width: 100%;
  max-height: 100%;
`;

export const StyledSidebarToggleIcon = styled.img<{
  position?: string;
  right?: string;
  top?: string;
  padding?: string;
  cursor?: string;
  height?: string;
  width?: string;
}>`
  position: ${(props) => props.position};
  right: ${(props) => props.right};
  top: ${(props) => props.top};
  padding: ${(props) => props.padding};
  cursor: ${(props) => props.cursor || 'pointer'};
  height: ${(props) => props.height};
  width: ${(props) => props.width};
  box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to bottom, #fff, #eee);
  border-radius: 50%;
`;

export const StyledFormControlLabel = styled(FormControlLabel)`
  margin-left: 0px !important;
  .MuiFormControlLabel-label {
    font-size: ${fontSize.b1};
    font-weight: ${fontWeight.medium};
    font-family: ${baseFontFamily};
    color: ${brand.black};
  }
`;

export const StyledText = styled.p<{
  fontSize?: string;
  color?: string;
  margin?: string;
  fontWeight?: string;
}>`
  font-size: ${(props) => props.fontSize};
  color: ${(props) => props.color};
  margin: ${(props) => props.margin};
  font-weight: ${(props) => props.fontWeight};
`;

export const StyledMenuItemText = styled(Typography)<{ active?: boolean }>`
  color: ${brand.white} !important;
  font-size: ${fontSize.b1} !important;
  margin: 0 !important;
  ${({ active }) =>
    active &&
    css`
      font-weight: ${fontWeight.medium} !important;
    `}
`;
