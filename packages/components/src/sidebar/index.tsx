import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import {
  StyledSidebarContainer,
  StyledSidebarInnerContainer,
  StyledHeadingIconContainer,
  StyledMenuItemContainer,
  StyledSidebarIcon,
  StyledSubMenuItemBoxContainer,
  StyledSubMenuItemContainer,
  StyledSubMenuItemText,
} from './styles.tsx';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

interface Props {
  onClose?: () => void;
  isOpen?: boolean;
  activeButton?: string;
  image: string;
  mainMenuItems: {
    key: string;
    label: any;
    path: string;
    icon: React.JSX.Element;
  }[];
  
}

export const Sidebar: React.FC<Props> = ({
  mainMenuItems,
  image,
}) => {
  const location = useLocation();
  const [secondaryMenuOpen, setSecondaryMenuOpen] = useState<{
    masterData: boolean;
    leadDataManagement: boolean;
  }>({
    masterData: false,
    leadDataManagement: false,
  });
  return (
    <StyledSidebarContainer>
      <StyledSidebarInnerContainer>
        <StyledHeadingIconContainer>
          <StyledSidebarIcon src={image} alt="logoicon" />
        </StyledHeadingIconContainer>
        <StyledMenuItemContainer>
          <StyledSubMenuItemBoxContainer>
            {mainMenuItems.map(({ key, label, path, icon }) => {
              const active = location.pathname.startsWith(
                `/${path.split('/')[1]}`
              );
              return (
                <StyledSubMenuItemContainer
                  draggable={false}
                  href={path}
                  active={active}
                  key={key}
                >
                  <StyledSubMenuItemText>{label}</StyledSubMenuItemText>
                  {active && (
                    <ChevronRightIcon style={{ marginLeft: 'auto' }} />
                  )}
                </StyledSubMenuItemContainer>
              );
            })}
          </StyledSubMenuItemBoxContainer>
        </StyledMenuItemContainer>
      </StyledSidebarInnerContainer>
    </StyledSidebarContainer>
  );
};

export default Sidebar;
