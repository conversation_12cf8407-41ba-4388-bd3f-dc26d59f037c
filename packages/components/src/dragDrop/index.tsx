import React, { useState, useRef } from "react";
import type { DragE<PERSON>, ChangeEvent } from "react";
import {
  DragDropContainer,
  ErrorMessage,
  FileInfo,
  FileName,
  HiddenInput,
  RemoveButton,
} from "./styles.tsx";

interface DragAndDropInputProps {
  onFileSelect?: (file: File) => void;
  acceptedTypes?: string[];
  maxSize?: number; // in bytes
  placeholder?: string;
}

const DragAndDropInput: React.FC<DragAndDropInputProps> = ({
  onFileSelect,
  acceptedTypes = [".csv", ".xlsx", ".xls"],
  maxSize = 5 * 1024 * 1024, // 5MB default
  placeholder = "Drag and drop a file here, or click to select",
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size must be less than ${Math.round(
        maxSize / (1024 * 1024)
      )}MB`;
    }

    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
    if (acceptedTypes.length > 0 && !acceptedTypes.includes(fileExtension)) {
      return `Only ${acceptedTypes.join(", ")} files are allowed`;
    }

    return null;
  };

  const handleFile = (file: File) => {
    const validationError = validateFile(file);

    if (validationError) {
      setError(validationError);
      setSelectedFile(null);
      return;
    }

    setError("");
    setSelectedFile(file);
    onFileSelect?.(file);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    if (selectedFile) return; // Block drop if file already exists

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFile(files[0]);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleClick = () => {
    if (!selectedFile) {
      fileInputRef.current?.click();
    }
  };

  const handleFileInput = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFile(files[0]);
    }
  };


  const handleRemoveFile = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedFile(null);
    setError("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <>
      <DragDropContainer
        isDragOver={isDragOver}
        hasFile={!!selectedFile}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        {selectedFile ? (
          <>
            <FileName>📄 {selectedFile.name}</FileName>
            <FileInfo>{formatFileSize(selectedFile.size)}</FileInfo>
            <RemoveButton onClick={handleRemoveFile}>Remove</RemoveButton>
          </>
        ) : (
          <div>{placeholder}</div>
        )}
      </DragDropContainer>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <HiddenInput
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(",")}
        onChange={handleFileInput}
      />
    </>
  );
};

export default DragAndDropInput;
