import {styled} from 'styled-components';

export 
const DragDropContainer = styled.div<{ isDragOver: boolean; hasFile: boolean }>`
  width: 100%;
  border: 2px dashed ${props => 
    props.hasFile ? '#4caf50' : 
    props.isDragOver ? '#2196f3' : 
    '#ccc'
  };
  border-radius: 8px;
  padding: 32px 16px;
  text-align: center;
  background-color: ${props => 
    props.hasFile ? '#f8fff8' :
    props.isDragOver ? '#f5f5f5' : 
    '#fafafa'
  };
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  
  &:hover {
    border-color: #2196f3;
    background-color: #f5f5f5;
  }
`;

export const FileName = styled.div`
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: 100%;
  padding: 0 8px;
`;

export const FileInfo = styled.div`
  margin-top: 8px;
  font-size: 14px;
  color: #666;
`;

export const ErrorMessage = styled.div`
  color: #f44336;
  font-size: 14px;
  margin-top: 8px;
`;

export const HiddenInput = styled.input`
  display: none;
`;

export const RemoveButton = styled.button`
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 8px;
  
  &:hover {
    background: #d32f2f;
  }
`;
