import { Alert } from '@mui/material';
import type { FC } from 'react';
import type { Grid2Props } from '@mui/material';
import { css, styled } from 'styled-components';
import { Grid2 } from '@mui/material';
export const StyledForm = styled.form<{ hasPadding?: boolean }>`
  ${({ hasPadding }) =>
    !hasPadding &&
    css`
      margin: 24px 24px 0 24px;
    `}
  display: grid;
`;
export const StyledFormRow: FC<Grid2Props> = styled(Grid2)`
  gap: 16px;
  margin-bottom: 16px;
`;

export const StyledFormRowItem: FC<Grid2Props> = styled(Grid2)`
  flex: 1;
`;

export const StyledFormError = styled(Alert)`
 max-width:402px;
`;
