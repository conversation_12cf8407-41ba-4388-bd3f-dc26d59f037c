import type { ButtonProps } from '@mui/material';
import React from 'react';
import { StyledButton, StyledIcon } from './styles.tsx';

type ButtonCustomProps = ButtonProps & {
  onClick?: (() => void) | ((event: React.MouseEvent<HTMLButtonElement>) => void);
  label?: string;
  img?: string;
  isImg?: boolean;
  isJsx?: boolean;
  JsxImg?: any;
};

const Button = ({ label,isJsx,JsxImg ,img, ...props }: ButtonCustomProps) => (
  <StyledButton isImg {...props} addMinPadding>
    {img && <StyledIcon src={img} />}
    {isJsx && <JsxImg />}
    {label}
  </StyledButton>
);

export default Button;
