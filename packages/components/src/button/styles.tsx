import type { FC, ImgHTMLAttributes } from 'react';
import Button from '@mui/material/Button';
import { respondTo } from '@mono/theme/style.layout.ts';
import { styled, css } from 'styled-components';

export const StyledButton = styled(Button)<{
  isImg?: boolean;
  addMinPadding?: boolean;
}>`
  ${isImg =>
    isImg &&
    `display:flex;
    flex-direction:row;
    gap:8px;
    `}
  white-space: nowrap;
  ${({ addMinPadding }) =>
    addMinPadding &&
    css`
      ${respondTo.mdDown} {
        padding: 4px 8px !important;
      }
    `}
`;
export const StyledIcon: FC<ImgHTMLAttributes<HTMLImageElement>> = styled.img``;
