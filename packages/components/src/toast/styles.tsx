import { Typography } from '@mui/material';
import { styled } from 'styled-components';
import { fontSize, fontWeight } from '@mono/theme/style.typography.ts';
import { brand, colors } from '@mono/theme/style.palette.ts';
export const StyledToastContainer = styled.div<{ type?: string }>`
   display: flex;
  align-items: center;
  gap: 12px;
  background-color: ${({ type }) => 
    type === "error" ? colors.errorBg : colors.successBg};
  color: ${brand.black};
  padding: 18px 16px;
  border-radius: 10px;
  border: 1px solid ${({ type }) =>
    type === "error" ? colors.errorDefault : colors.successDefault};
  width: 100%;
  height:100%;
  max-width: 400px;
`;

export const StyledToastIcon = styled.img`
  width: 27px;
  height: 27px;
`;

export const StyledToastInfoContainer = styled.div`
  border-radius: 14px;
  display: flex;
  flex-direction: column;
`;

export const StyledToastInfoText = styled(Typography)`
  font-weight: ${fontWeight.semiBold} !important;
  font-size: ${fontSize.h5} !important;
  color: ${colors.black} !important;
`;

export const StyledToastInfoSubText = styled(Typography)`
  font-size: ${fontSize.b1} !important;
  color: ${colors.black} !important;
  font-weight: ${fontWeight.semiBold} !important;
`;
