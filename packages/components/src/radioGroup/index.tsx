import React, { useEffect } from 'react';
import { FormControlLabel, FormLabel, Radio, RadioGroup } from '@mui/material';
import { styled } from '@mui/material/styles';
import { brand, colors } from '@mono/theme/style.palette.ts';

export interface Option {
  id: number | string;
  label: string;
}

interface Props {
  label: string;
  options: Option[];
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disableErrorMode?: boolean;
}

// Styled Components
const StyledInputContainer = styled('div')({
  display: 'flex',
  flexDirection: 'column',
});

const StyledError = styled('p')(({ theme }) => ({
  color: `${colors.errorDefault}`,
  fontSize: '12px',
  margin: 0,
}));

const StyledFormLabel = styled(FormLabel)({
  color: '#808080', // Grey text color
  fontWeight: 500,
});

const StyledRadio = styled(Radio)({
  color: '#808080',
  background:'transparent', // Default grey color for unselected
  '&.Mui-checked': {
    color: '#54AFC7',
    border:'none' // Highlight color for selected radio
  },
  '&:hover': {
    backgroundColor: 'transparent', // Removes the hover background effect
  },
});

const ReadioGroupInput: React.FC<Props> = ({
  label,
  options,
  value,
  onChange,
  error,
  disableErrorMode,
}) => {
  return (
  <StyledInputContainer>
    <RadioGroup
      row
      value={value}
      onChange={(event) => {
        if (onChange) {
          console.log('event?.currentTarget?.value', event?.currentTarget?.value);
          onChange(event?.currentTarget?.value);
        }
      }}
    >
      {options?.map(({ label, id }) => {
        const isActive = value === id;
        return (<FormControlLabel
          key={`radio-${id}`}
          control={<StyledRadio checked={isActive}  />}
          value={id}
          label={label}
          sx={{ color: `${brand.black}` }} // Grey text for unselected options
        />
      )})}
    </RadioGroup>
    {!disableErrorMode && error && <StyledError>{error}</StyledError>}
  </StyledInputContainer>)
};

export default ReadioGroupInput;
