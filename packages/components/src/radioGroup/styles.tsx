import { FormLabel, Typography } from '@mui/material';
import {styled} from 'styled-components';
import { colors } from '@mono/theme/style.palette.ts';
import { fontSize, fontWeight } from '@mono/theme/style.typography.ts';


export const StyledLabel = styled(FormLabel)<{isQuestionModal?: boolean}>`
 font-weight: ${({isQuestionModal}) => isQuestionModal ? `${fontWeight.medium} !important` : `${fontWeight.bold} !important`};
 font-size:  ${({isQuestionModal}) => isQuestionModal && `${fontSize.h5} !important`};
 color: black !important;

`