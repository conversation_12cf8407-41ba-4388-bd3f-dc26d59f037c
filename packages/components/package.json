{"name": "@mono/components", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/runtime": "^7.27.6", "@mono/messages": "workspace:*", "@mono/models": "workspace:*", "@mono/redux-global": "workspace:*", "@mono/theme": "workspace:*", "@mono/utils": "workspace:*", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@mui/x-date-pickers": "^7.29.4", "moment": "^2.30.1", "mui-daterange-picker": "^1.0.5", "react": "^19.1.0", "react-circular-progressbar": "^2.2.0", "react-lottie": "^1.2.10", "react-redux": "^9.2.0", "react-router-dom": "^5.2.0", "styled-components": "^6.1.19"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-lottie": "^1.2.10", "@types/react-router-dom": "^5.1.6"}}