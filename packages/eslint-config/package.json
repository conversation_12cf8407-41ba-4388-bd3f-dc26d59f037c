{"name": "@mono/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}