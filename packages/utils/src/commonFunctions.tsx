import moment from 'moment';
import type { Moment } from 'moment';
import momenttz from 'moment-timezone';
import type { Id, Option } from '@mono/models';

export const convertIsoDatoToIsoDateTime = (
  date?: string
): string | undefined => {
  if (!date) {
    return undefined;
  }
  return `${date}T${moment().format('HH:mm:ssZ')}`;
};

export const dateFormatterFunction = (
  date: string | Moment,
  formatType: string = 'DD MMM YYYY'
) => moment(date).format(formatType);

export const getTimeZoneForLocation = (location: string) => {
  switch (location) {
    case 'AUSTRALIA':
      return 'Australia/Melbourne';
    default:
      return 'America/Los_Angeles';
  }
};

export const dateFormatterFunctionTimeZone = (
  date: string | Moment,
  formatType: string = 'DD MMM YYYY',
  timeZone: string = 'UTC' // Default to UTC
) => momenttz(date).tz(timeZone).format(formatType);

export const dateFormatterUTC = (
  date: string,
  formatType: string = 'DD MMM YYYY',
  timeZone: string = Intl.DateTimeFormat().resolvedOptions().timeZone
) => {
  return momenttz.utc(date).tz(timeZone).format(formatType);
};

export const roundToOneDecimal = (value: number) => Math.round(value * 10) / 10;

export const capitalizeEntireString = (
  str: string | number
): string | undefined => {
  if (!str) {
    return null;
  }
  const stringToCapitalize = str as string;
  return stringToCapitalize?.toUpperCase();
};

export const mapIdNameToOptionWithoutCaptializing = (entity: {
  id: Id;
  name: string;
}): Option => ({ id: entity?.id, label: trimWordWrapper(entity?.name) });

export const convertToIsoDateTime = (date?: string): string | undefined => {
  if (!date) {
    return undefined;
  }
  return moment(date).format('YYYY-MM-DDTHH:mm:ssZ');
};

export const convertToIsoDate = (date?: string): string | undefined => {
  if (!date) {
    return undefined;
  }
  return moment(date).format('YYYY-MM-DD');
};

export const isUndefined = (value: unknown): boolean => value === undefined;
export const isNull = (value: unknown): boolean => value === null;

export const getApiDate = (
  value: string | moment.Moment | undefined | null
): string | undefined | null => {
  if (isNull(value)) return null;
  if (isUndefined(value)) return undefined;
  return convertToIsoDate(value as string);
};

export const convertToMomentDate = (
  value: string | moment.Moment | undefined | null
): moment.Moment | undefined | null => {
  if (isNull(value)) return null;
  if (isUndefined(value)) return undefined;
  return moment(value);
};

/* eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types */
export const getEditUrl =
  (route: string) =>
  (entity: any): string =>
    route.replace(':id', entity?.id);

export const convertSingleToDoubleDigit = (
  value?: number
): string | undefined => {
  if (isNull(value)) return null;
  if (isUndefined(value)) return undefined;

  if (value >= 0 && value <= 9) {
    return `0${value}`;
  }
  return `${value}`;
};

export const fileSizeCheckFunction = (
  fileSize: number,
  acceptedFileSize: number
) => {
  if (fileSize / 1024 / 1024 >= acceptedFileSize) {
    return true;
  }
  return false;
};

export const capitalizeLegend = (str?: string) => {
  if (str === null || str === undefined) {
    return str;
  }
  return `${str?.charAt(0)?.toUpperCase()}${str
    ?.slice(1)
    ?.toLowerCase()
    .replace('_', ' ')}`;
};

export const mapIdNameToOption = (entity: {
  id: Id;
  name: string;
}): Option => ({ id: entity?.id, label: capitalizeLegend(entity?.name) });

export const underscoreChangeFunction = (str: string): string => {
  if (!str.includes('_')) {
    return str;
  }
  return str.replace('_', ' ');
};

export const trimWordWrapper = (str: string): string => str.trim();

export const capitalize = (str: string) => {
  return str?.charAt(0)?.toUpperCase() + str?.slice(1)?.toLowerCase();
};
export const formatStatus = (status: string) => {
  let formatted = status
    ?.replace(/_AND_/gi, ' & ') // Replace "_AND_" with " & "
    ?.replace(/\bAND\b/gi, '&') // Replace standalone "AND" with "&" (case-insensitive)
    ?.split(/[_\s]+/) // Split by underscores or spaces
    ?.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize first letter
    ?.join(' ');
  if (formatted === 'Vision Mission & Values') {
    formatted = 'Vision, Mission & Values';
  }
  return formatted;
};

export const darkenColor = (color: string, percent: number) => {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) - amt;
  const G = ((num >> 8) & 0x00ff) - amt;
  const B = (num & 0x0000ff) - amt;
  return `#${(
    0x1000000 +
    (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
    (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
    (B < 255 ? (B < 1 ? 0 : B) : 255)
  )
    .toString(16)
    .slice(1)}`;
};

export const lightenColor = (color: string, percent: number) => {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = ((num >> 8) & 0x00ff) + amt;
  const B = (num & 0x0000ff) + amt;
  return `#${(
    0x1000000 +
    (R > 255 ? 255 : R) * 0x10000 +
    (G > 255 ? 255 : G) * 0x100 +
    (B > 255 ? 255 : B)
  )
    .toString(16)
    .slice(1)}`;
};

export const scrollToStepTop = () => {
  console.log('scrollToTop', document);
  if (!document) return;
  document?.getElementById('step-start')?.scrollIntoView();
};

export const extractErrorMessage = (
  errorMessages: Record<string, string>,
  errorCode: string
): string => {
  return errorMessages[errorCode] || 'An unexpected error occurred';
};

// Perform rollback for failed operations
export const performRollback = async (
  rollbackStack: (() => Promise<void>)[]
) => {
  while (rollbackStack.length) {
    const rollbackFn = rollbackStack.pop();
    if (rollbackFn) {
      try {
        await rollbackFn();
      } catch (err) {
        console.error('Rollback failed:', err);
      }
    }
  }
};

export const flattenObject = <T extends Record<string, unknown>>(
  obj: T,
  parentKey = '',
  separator = '_'
): Record<string, unknown> => {
  const flatObject: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(obj)) {
    const newKey = parentKey ? `${parentKey}${separator}${key}` : key;

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      Object.assign(
        flatObject,
        flattenObject(value as Record<string, unknown>, newKey, separator)
      );
    } else {
      flatObject[newKey] = value;
    }
  }

  return flatObject;
};

export const transformArrayToObject = <T extends { id: string | number }>(
  dataArray: T[]
): Record<string | number, T> => {
  return dataArray.reduce(
    (acc, item) => {
      if (item.id != null) {
        acc[item.id] = item;
      }
      return acc;
    },
    {} as Record<string | number, T>
  );
};
