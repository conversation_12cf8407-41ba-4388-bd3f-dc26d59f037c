{"name": "obiemoney-client", "private": true, "version": "1.0.0", "type": "module", "description": "ObieMoney Frontend Client Application", "scripts": {"start": "webpack serve --config webpack/webpack.local.js --host=0.0.0.0", "build": "webpack --config webpack/webpack.prod.js", "build:prod": "webpack --config webpack/webpack.config.js --env env=prod", "build:stage": "webpack --config webpack/webpack.config.js --env env=stage", "build:dev": "webpack --config webpack/webpack.config.js --env env=dev", "lint": "eslint . --fix --color"}, "dependencies": {"@babel/plugin-syntax-flow": "^7.27.1", "@babel/runtime": "^7.27.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mono/components": "workspace:*", "@mono/hooks": "workspace:*", "@mono/messages": "workspace:*", "@mono/models": "workspace:*", "@mono/redux-global": "workspace:*", "@mono/theme": "workspace:*", "@mono/utils": "workspace:*", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@mui/styles": "^6.4.0", "@mui/x-data-grid": "^7.25.0", "@mui/x-date-pickers": "^7.23.6", "@reduxjs/toolkit": "^2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "connected-react-router": "^6.9.3", "history": "^4.10.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jest-styled-components": "^7.2.0", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "mui-daterange-picker": "^1.0.5", "papaparse": "^5.5.3", "react": "^19.1.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-intl-redux": "^2.4.1", "react-lottie": "^1.2.4", "react-redux": "^9.2.0", "react-router-dom": "^5.2.0", "react-toastify": "^10.0.6", "recharts": "^2.15.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "styled-components": "^6.1.19", "uuid": "^11.0.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@mono/eslint-config": "workspace:*", "@mono/typescript-config": "workspace:*", "@types/history": "^4.7.11", "@types/md5": "^2.3.5", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.1.6", "@types/redux-mock-store": "^1.5.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.10.2", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.6.3", "style-loader": "^3.3.3", "typescript": "~5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1"}}