import React, { type JSX } from 'react';
import { Head<PERSON>, Modal } from '@mono/components';
import { useDispatch, useSelector } from 'react-redux';
import {
  StyledChildren<PERSON>ontainer,
  StyledContainer,
  Styled<PERSON>ontentContainer,
  StyledLogoutMenuIcon,
  StyledIcon,
} from './styles';
import { routes } from '../myUtils';
import { logout } from '@mono/redux-global/src/actions';
import type { ReduxState } from '../redux/reducers';
import messages from '../messages';
import { usePopupReducer } from '@mono/hooks';
import ChangePassword from '../screens/auth/changePassword';

interface Props {
  children?: JSX.Element | JSX.Element[];
  hideSidebar?: boolean;
  cardCss?: React.CSSProperties;
  contentCss?: React.CSSProperties;
  heading?: string;
  showGoBack?: boolean;
  hasHeader?: boolean;
  iconSource?: string;
  goBackIcon?: { isBack: boolean; path: string };
  noPadding?: boolean;
  noMargin?: boolean;
}

const Container: React.FC<Props> = ({
  children,
  hasHeader = true,
  noPadding,
  noMargin = false,
  ...styleProps
}) => {
  const reduxDispatch = useDispatch();
  const actions = [
    {
      id: 'changePassword',
      text: messages?.changePassword?.heading,
      onClick: () => {
        showForm();
      },
      icon: (
        <StyledIcon
          src={'../assets/images/password.svg'}
          alt={messages?.general?.password || 'icon'}
        />
      ),
    },
    {
      id: 'logout',
      text: messages?.general?.logout,
      onClick: () => {
        reduxDispatch(logout());
      },
      icon: <StyledLogoutMenuIcon />,
    },
  ];

  const mainMenuItems = [
    {
      key: 'dashboard',
      label: messages?.sidebar?.menuItems?.dashboard?.heading,
      path: routes.dashboard.root,
    },
  ];

  const userProfile = useSelector((state: ReduxState) => state.profile);
  const {
    visibility: formVisibility,
    showPopup: showForm,
    hidePopup: hideForm,
  } = usePopupReducer();
  return (
    <StyledContainer noPadding={noPadding} {...styleProps}>
      <StyledContentContainer noMargin={noMargin}>
        {hasHeader && (
          <Header
            actions={actions}
            panel={messages?.general?.admin}
            mainMenuItems={mainMenuItems}
            image={'../assets/images/ul-logo.png'}
            userProfile={userProfile}
          />
        )}
        <StyledChildrenContainer noMargin={noMargin} hasHeader={hasHeader}>
          {children}
        </StyledChildrenContainer>
      </StyledContentContainer>
      <Modal
        show={formVisibility}
        heading={messages?.changePassword?.heading}
        onClose={hideForm}
        fitContent
      >
        <ChangePassword onCancel={hideForm} onSuccess={hideForm} />
      </Modal>
    </StyledContainer>
  );
};

export default Container;
