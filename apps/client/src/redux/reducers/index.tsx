import { combineReducers } from 'redux';
import { connectRouter } from 'connected-react-router';
import type { RouterState } from 'connected-react-router';
import type { History } from 'history';
import type { LoaderState, StepFormState } from '@mono/models';
import auth from '@mono/redux-global/src/reducers/auth';
import type { AuthState } from '@mono/redux-global/src/reducers/auth';
import { createBasicReducer } from '@mono/redux-global/src/reducers/utils';
import {
  SYSTEM_LOADER,
  USER_PROFILE,
  // Add more Action types here
} from '@mono/redux-global/src/actions';

export interface UserProfile {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  dialCode: string;
  role: { id: string };
  profilePhoto: string;
}

export interface ReduxState {
  router: RouterState<unknown>;
  auth: AuthState;
  loader: LoaderState;
  stepForm?: StepFormState;
  profile?: UserProfile;
}

export interface StoreStates {
  router: RouterState;
  auth: AuthState;
  loader: LoaderState;
  stepForm: StepFormState;
}
const loaderReducer = createBasicReducer<LoaderState>(SYSTEM_LOADER, {
  visibility: false,
});
const createRootReducer = (history: History) =>
  combineReducers({
    router: connectRouter(history),
    auth,
    profile: createBasicReducer<UserProfile>(USER_PROFILE, {
      id: 0,
      name: '',
      email: '',
      phoneNumber: '',
      dialCode: '',
      role: { id: '' },
      profilePhoto: '',
    }),
    loader: loaderReducer,
  });

export default createRootReducer;
