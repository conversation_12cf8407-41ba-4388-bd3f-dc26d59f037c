import React,{ type JSX } from "react";
import { StyledAuthBanner, StyledBottomImage,StyledBannerTitle, StyledLogo, StyledDiv, StyledAuthContainter, StyledAuthLayout, StyledBannerImage, StyledPrimaryWrapper,StyledSecondaryWrapper } from "./styles";
import messages from "../../messages";

interface AuthLayout{
    children:JSX.Element
}
const AuthLayout =({children}:AuthLayout)=>{
    return (
        <>
            <StyledAuthLayout>
                <StyledPrimaryWrapper>
                <StyledAuthBanner>
                    <StyledBannerTitle>{messages?.login?.bannerTitle}</StyledBannerTitle>
                    <StyledBottomImage>
                         <StyledBannerImage src={'../../assets/images/authBanner.png'} alt="bannerImg"/> 
                    </StyledBottomImage>
                </StyledAuthBanner>
                </StyledPrimaryWrapper>
                <StyledSecondaryWrapper>
                <StyledAuthContainter>
                        <StyledLogo src={'../../assets/images/ul-logo.png'} alt="logo" />
                    <StyledDiv>
                        {children}
                    </StyledDiv>
                </StyledAuthContainter>
                </StyledSecondaryWrapper>
            </StyledAuthLayout>
        </>
    )
}

export default AuthLayout;