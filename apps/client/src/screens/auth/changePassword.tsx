import React from 'react';
import { useFormReducer } from '@mono/hooks';
import { confirmPassword, HttpMethods, required } from '@mono/utils';
import {
  Button,
  Form,
  FormError,
  FormRow,
  FormRowItem,
  PasswordInput,
  Toast,
} from '@mono/components';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StyledButtonDiv,
  StyledP<PERSON><PERSON><PERSON><PERSON><PERSON>,
  StyledPopUpContainer,
} from './styles';
import messages from '@mono/messages';
import md5 from 'md5';
import { useDispatch } from 'react-redux';
import { RESET_PASSWORD } from '../../api';
import { apiCall } from '@mono/redux-global/src/actions';
import { passwordValidator } from '@mono/utils';
import type { ModalProps } from '../../models';
import { toast } from 'react-toastify';
import type { FormValue } from '@mono/models';

const validators = {
  oldPassword: [required(messages?.changePassword?.currentPassword)],
  newPassword: [
    required(messages?.changePassword?.newPassword),
    passwordValidator,
  ],
  confirmPassword: [
    required(messages?.changePassword?.confirmPassword),
    confirmPassword(messages?.changePassword?.retypePassword),
  ],
};

const ChangePassword: React.FC<ModalProps> = ({ onCancel, onSuccess }) => {
  const {
    submitting,
    submitError,
    handleSubmit,
    connectField,
    setSubmitError,
    stopSubmitting,
  } = useFormReducer(validators);
  const reduxDispatch = useDispatch();
  const onSubmit = async (data: Record<string, FormValue>) => {
    return new Promise<void>((resolve, reject) => {
      const sanitizedBody = {
        oldPassword: md5(String(data?.oldPassword || '')),
        newPassword: md5(String(data?.newPassword || '')),
      };

      reduxDispatch(
        apiCall(
          RESET_PASSWORD,
          resolve,
          reject,
          HttpMethods.POST,
          sanitizedBody
        )
      );
    })
      .then(() => {
        toast(
          <Toast subText={messages?.changePassword?.success?.passwordChanged} />
        );
        onSuccess();
      })
      .catch(error => {
        console.log(error?.message, 'err');
        setSubmitError(error?.message);
        stopSubmitting();
      });
  };

  return (
    <>
      <Form onSubmit={handleSubmit(onSubmit)} hasPadding>
        <StyledPopUpContainer>
          <FormRow>
            <FormRowItem>
              {connectField('oldPassword', {
                label: messages?.changePassword?.currentPassword,
                required: true,
              })(PasswordInput)}
            </FormRowItem>
          </FormRow>
          <FormRow>
            <FormRowItem>
              {connectField('newPassword', {
                label: messages?.changePassword?.newPassword,
                required: true,
              })(PasswordInput)}
            </FormRowItem>
          </FormRow>
          <FormRow>
            <FormRowItem>
              {connectField('confirmPassword', {
                label: messages?.changePassword?.confirmPassword,
                required: true,
              })(PasswordInput)}
            </FormRowItem>
          </FormRow>

          {submitError && (
            <FormRow>
              <FormRowItem>
                <FormError
                  message={
                    (messages?.changePassword?.errors as Record<string, string>)?.[submitError]
                  }
                />
              </FormRowItem>
            </FormRow>
          )}
        </StyledPopUpContainer>
        <StyledPopUpbottom>
          <ButtonWrapper>
            <StyledButtonDiv>
              <Button
                label={messages?.general?.cancel}
                variant="outlined"
                color="secondary"
                size="small"
                onClick={onCancel}
                disabled={submitting}
              />
            </StyledButtonDiv>
            <StyledButtonDiv>
              <Button
                label={messages?.general?.update}
                type="submit"
                variant="contained"
                color="primary"
                size="small"
                disabled={submitting}
              />
            </StyledButtonDiv>
          </ButtonWrapper>
        </StyledPopUpbottom>
      </Form>
    </>
  );
};

export default ChangePassword;
