import { useDispatch } from 'react-redux';
import md5 from 'md5';
import { emailValidator, required } from '@mono/utils';
import {
  Button,
  Form,
  FormError,
  FormRow,
  FormRowItem,
  PasswordInput,
  TextInput,
} from '@mono/components';
import { useFormReducer } from '@mono/hooks';
import messages from '@mono/messages';
import {
  StyledFormContainer,
  StyledFormHeading,
  StyledFormSubHeading,
  StyledInfoContainer,
  StyledScreenWrapper,
} from './styles';
import { login } from '@mono/redux-global/src/actions';

import AuthLayout from './AuthLayout';
import type { FormValue } from '@mono/models';
import { push } from 'connected-react-router';
import type { UnknownAction } from 'redux';
import { routes } from '../../myUtils';

const validators = {
  email: [emailValidator],
  password: [required('')],
};

const Login = () => {
  const {
    submitting,
    submitError,
    handleSubmit,
    connectField,
    setSubmitError,
  } = useFormReducer(validators);
  const reduxDispatch = useDispatch();
  const onSubmit = async (data: Record<string, FormValue>) => {
    const email = String(data.email || '');
    const password = String(data.password || '');

    return new Promise<void>((resolve, reject) => {
      reduxDispatch(push(routes?.dashboard?.root) as unknown as UnknownAction);
      reduxDispatch(login({ email, password: md5(password) }, resolve, reject));
    }).then(() => {
    })
    .catch((error: { message?: string }) => {
      setSubmitError(error?.message ?? 'Login failed');
    });
  };

  return (
    <>
      <AuthLayout>
        <StyledScreenWrapper>
          <StyledFormContainer>
            <StyledInfoContainer>
              <StyledFormHeading variant="h2">
                {messages?.login?.heading}
              </StyledFormHeading>
              <StyledFormSubHeading>
                {messages?.login?.subHeading}
              </StyledFormSubHeading>
            </StyledInfoContainer>
            <Form onSubmit={handleSubmit(onSubmit)} hasPadding>
              <FormRow>
                <FormRowItem>
                  {connectField('email', {
                    label: messages?.login?.form?.email,
                    required: true,
                  })(TextInput)}
                </FormRowItem>
              </FormRow>
              <FormRow>
                <FormRowItem>
                  {connectField('password', {
                    label: messages?.login?.form?.password,
                    required: true,
                  })(PasswordInput)}
                </FormRowItem>
              </FormRow>

              {submitError ? (
                <FormRow>
                  <FormRowItem>
                    <FormError
                      message={messages?.login?.form?.errors?.invalidDetails}
                    />
                  </FormRowItem>
                </FormRow>
              ) : (
                <></>
              )}

              <FormRow>
                <FormRowItem>
                  <Button
                    label={messages?.login?.form?.logIn}
                    type="submit"
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={submitting}
                  />
                </FormRowItem>
              </FormRow>
            </Form>
          </StyledFormContainer>
        </StyledScreenWrapper>
      </AuthLayout>
    </>
  );
};

export default Login;
