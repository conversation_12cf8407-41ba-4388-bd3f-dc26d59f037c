import {
  Grid2, <PERSON>, Typography, Chip,
  FormControlLabel,
} from '@mui/material';
import styled from 'styled-components';
import { respondTo } from '@mono/theme/style.layout';
import { fontSize, fontWeight } from '@mono/theme/style.typography';
import { brand, greyScaleColour } from '@mono/theme/style.palette';


export const StyledPanel = styled.div`
  display: flex;
  position: relative;
  width: 663px;
  height: calc(100vh - 50px);
  border-radius: 22px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  
  ${respondTo.mdDown} {
    width: 450px;
  }
  ${respondTo.screenDown(850)} {
    width: 360px;
  }
  ${respondTo.smOnly} {
    display: none;
  }
`;

export const StyledSidePanelLogoImage = styled.img`
  height: 31px;
  width: 133px;
  position: absolute;
  top: 70px;
  left: 44px;
`;

export const StyledSidePanelUpperMiddleText = styled(Typography)`
  position: absolute;
  top: 507px;
  left: 44px;
  font-weight: ${fontWeight.semiBold} !important;
  line-height: 60px !important;
  font-size: 55px !important;
  color: ${greyScaleColour.white100} !important;
  height: 120px;
  width: 301px;
`;
export const StyledSidePanelLowerMiddleText = styled(Typography)`
  position: absolute;
  top: 633px;
  left: 44px;
  line-height: 20px !important;
  color: ${greyScaleColour.white100} !important;
  height: 40px;
  width: 383px;
`;

export const StyledSidePanelLowerText = styled(Typography)`
  position: absolute;
  top: 777px;
  left: 44px;
  line-height: 18px !important;
  color: ${greyScaleColour.grey60} !important;
  height: 40px;
  width: 383px;
`;

export const StyledGridContainer = styled(Grid2)`
  padding: 40px;
  padding-bottom: 142px;
  display: flex;
  flex-direction: column;
  color : white;
`;

export const StyledScreenWrapper = styled.div`
  display: flex;
  ${respondTo.mdDown} {
    gap: 20px;
  }
`;

export const StyledFormContainer = styled.div`
  align-self: center;
  display:flex;
  flex-direction:column;
  gap:20px;
  width:100%;
  ${respondTo.mdDown} {
    width: 300px;
  }
`;

export const StyledInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 443px;
`;
export const StyledFormHeading = styled(Typography)`
  font-weight: ${fontWeight.semiBold} !important;
  font-size: 40px !important;
  line-height: 60px !important;
  color:${brand.black};
`;
export const StyledFormSubHeading = styled(Typography)`
  font-weight: ${fontWeight.regular} !important;
  font-size: ${fontSize.h5} !important;
  color: ${greyScaleColour.grey100};
  line-height: 24px !important;
`;
export const StyledLink = styled(Link)`
  font-size: ${fontSize.b2} !important;
  font-weight: ${fontWeight.regular} !important;
`;

export const StyledFormControlLabel = styled(FormControlLabel)`
  margin-left: 0px !important;
  color: ${greyScaleColour.grey100} !important;
`;

export const StyledLogoContainer = styled(Grid2)`
  width: 141px;
  height: 37px;
`;


export const StyledBannerImage = styled.img``

export const StyledPanelInfo = styled(Typography)`
  color: ${brand.white};
  font-size: 56px !important;
  font-weight: ${fontWeight.semiBold} !important;
  line-height: 120% !important;
  ${respondTo.mdDown} {
    font-size: 44px !important;
  }
`;
export const StyledPanelSubInfo = styled(Typography) <{
  fontsize?: string;
  fontweight?: number;
}>`
  color: ${brand.white};
  font-size: ${({ fontsize }) => fontsize || fontSize.b1} !important;
  font-weight: ${({ fontweight }) => fontweight || fontWeight.regular} !important;
  line-height: normal !important;
  ${respondTo.mdDown} {
    font-size: ${fontSize.b2} !important;
  }
`;
export const StyledWord = styled.span`
  color: ${brand.primaryMain};
`;

export const StyledLinkContainer = styled.a`
  &:hover {
    color: ${brand.primaryMain} !important;
    & .MuiSvgIcon-root {
      color: ${brand.primaryMain} !important;
    }
  }
  text-decoration: none;
`;

export const StyledImageContainer = styled(Grid2)`
  padding: 0 !important;
  padding-left: 1px !important;
`;

export const StyledImageContent = styled(Grid2)`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 113px;
  padding: 0 !important;
`;

export const StyledLoginChip = styled(Chip)`
  &&.MuiChip-root {
    width:103px;
    height: 26px !important;
    padding: 12px 4px !important;
    font-size: ${fontSize.b2} !important;
    background-color: ${brand.primary20} !important;
    font-weight: ${fontWeight.medium} !important;
    color: ${brand.primaryMain} !important;
  }
`;
export const StyledPrimaryWrapper = styled.div`
  width:50%;
  display:flex;
  justify-content:flex-start;
`
export const StyledSecondaryWrapper = styled.div`
  width:50%;
  display:flex;
  justify-content:center;
`
export const StyledAuthLayout = styled.div`
  padding:24px;
  border-radius:24px;
  display:flex;
  ${respondTo.screenDown(981)} {
  gap:20px;
}
`
export const StyledAuthBanner = styled.div`
  width:717px;
  border-radius:24px;
  background-color:${brand.primaryMain};
  color:${brand.white};
  background-color: ${brand.primaryMain};
  background-image: url('../../assets/images/AuthBg.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  height:95vh;
    position: relative; /* Establishes a containing block for the child */
  display: grid;
  grid-template-rows: auto 1fr; 
  align-items: end; 
  overflow: hidden; 
  position: relative;

`


export const StyledBannerTitle = styled.h2`
  max-width:514px;
  font-weight:${fontWeight.semiBold};
  font-size:${fontSize.title};
  line-height:65px;
  padding:0 24px;
`
export const StyledAuthContainter = styled.div`
  display:flex;
  flex-direction:column;
  width:443px;
  padding:24px;
  gap:4.5rem;
`
export const StyledPopUpContainer = styled.div`
  padding:24px;
  max-width:420px;
`
export const StyledPopUpbottom = styled.div`
  padding: 24px;
  border-top: 1px solid ${greyScaleColour.grey10};
`
export const StyledButtonDiv = styled.div`
  max-width:83px;
`
export const ButtonWrapper = styled.div`
  display:flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap:nowrap;
  justify-content:flex-end;
  margin-bottom:0;

`
export const StyledErrorWrapper = styled.div`
  max-width:402px;
`



export const StyledBottomImage = styled.div`
  width: 100%;
  height: 100%; 
  text-align: left; /* Align image container to the left */
  z-index: 1;
  display: flex; 
  justify-content: flex-start; /* Align image to the left */
  align-items: flex-start; /* Align with StyledDiv */
  position: relative;
  img {
    width: 100%;  /* Ensure full width */
    max-width: none;
    object-fit: cover;
    border-radius: 0; 
    position: absolute;
    bottom: 0px;
    left: 0;
  }
`

export const StyledDiv = styled.div`
  margin-top: 18px;
  display: flex;
  justify-content: flex-start; /* Align items to the left */
  align-items: center;
  width: 100%;
`;

export const StyledLogo = styled.img`
    max-width: 200px;
    height: auto;
    position: relative;
    left: -48px;
`;