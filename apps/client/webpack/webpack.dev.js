import webpack from 'webpack';

const processEnv = new webpack.DefinePlugin({ 
  'process.env.ENV_NAME': JSON.stringify('dev') 
});

export default {
  mode: 'development', // Changed from 'production'
  devtool: 'eval-cheap-module-source-map',
  performance: {
    hints: false,
    maxEntrypointSize: 512000,
    maxAssetSize: 512000,
  },
  plugins: [processEnv],
  devServer: {
    port: 3000,
    hot: true,
    open: true,
    historyApiFallback: true,
  },
};