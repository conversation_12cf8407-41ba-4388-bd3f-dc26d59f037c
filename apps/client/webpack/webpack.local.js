import { merge } from 'webpack-merge';
import common from './webpack.common.js';

import webpack from 'webpack';

const processEnv = new webpack.DefinePlugin({
  'process.env.ENV_NAME': JSON.stringify('local'),
});

export default merge(common, {
  mode: 'development',
  devtool: 'cheap-module-source-map',
  devServer: {
    hot: true,
    open: true,
    historyApiFallback: true,
    port: 3001,
  },
  plugins: [processEnv],
});
