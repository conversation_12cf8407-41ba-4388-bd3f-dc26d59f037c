import { merge } from 'webpack-merge';
import common from './webpack.common.js';
import webpack from 'webpack';

const processEnv = new webpack.DefinePlugin({ 'process.env.ENV_NAME': JSON.stringify('prod') });

export default merge(common, {
  mode: "production",
  devtool: "source-map",
  performance: {
    hints: false,
    maxEntrypointSize: 512000,
    maxAssetSize: 512000,
  },
  plugins: [
    processEnv,
  ],
});
