# ObieMoney - Financial Management Platform

A modern, full-stack financial management platform built with React, TypeScript, and a monorepo architecture.

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- pnpm (v8 or higher)

### Installation
```bash
# Install dependencies
pnpm install

# Start development servers
pnpm dev

# Or start individual services
pnpm dev:client  # Frontend on port 3001
pnpm dev:server  # Backend on port 8080
```

## 📁 Project Structure

```
obiemoney/
├── apps/
│   ├── client/          # React frontend application
│   └── server/          # Backend API server (to be added)
├── packages/
│   ├── components/      # Shared UI components
│   ├── hooks/          # Shared React hooks
│   ├── models/         # TypeScript type definitions
│   ├── redux/          # Global state management
│   ├── theme/          # UI theme and styling
│   └── utils/          # Shared utilities
└── package.json        # Root package configuration
```

## 🛠️ Available Scripts

- `pnpm dev` - Start all development servers
- `pnpm dev:client` - Start frontend development server
- `pnpm dev:server` - Start backend development server
- `pnpm build` - Build all packages
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all packages
- `pnpm format` - Format code with Prettier

## 🏗️ Tech Stack

### Frontend
- **React 19** - UI framework
- **TypeScript** - Type safety
- **Material-UI** - Component library
- **Redux Toolkit** - State management
- **React Router** - Navigation
- **Webpack** - Module bundler

### Backend (Planned)
- **Node.js** - Runtime
- **Express** - Web framework
- **TypeScript** - Type safety
- **PostgreSQL** - Database

## 📦 Monorepo Structure

This project uses pnpm workspaces for efficient dependency management and code sharing across packages.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
